<?php $__env->startSection('content'); ?>
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" class="text-decoration-none text-dark">Home</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>" class="text-decoration-none text-dark">My Account</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Orders</li>
                </ol>
            </nav>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <h1 class="fw-bold mb-4">My Orders</h1>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-3 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="rounded-circle bg-dark text-white d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;">
                            <?php echo e(auth()->user()->initials()); ?>

                        </div>
                        <div>
                            <h5 class="fw-bold mb-0"><?php echo e(auth()->user()->name); ?></h5>
                            <p class="text-muted mb-0"><?php echo e(auth()->user()->email); ?></p>
                        </div>
                    </div>
                    
                    <div class="list-group list-group-flush">
                        <a href="<?php echo e(route('dashboard')); ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                        </a>
                        <a href="<?php echo e(route('orders.index')); ?>" class="list-group-item list-group-item-action active">
                            <i class="fas fa-shopping-bag me-2"></i> My Orders
                        </a>
                        <a href="<?php echo e(route('wishlist.index')); ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-heart me-2"></i> My Wishlist
                        </a>
                        <a href="<?php echo e(route('settings.profile')); ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-user-cog me-2"></i> Account Settings
                        </a>
                        <form method="POST" action="<?php echo e(route('logout')); ?>">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="list-group-item list-group-item-action text-danger">
                                <i class="fas fa-sign-out-alt me-2"></i> Logout
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-9">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-0">
                    <?php if(auth()->user()->orders()->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th scope="col" class="border-0">Order</th>
                                        <th scope="col" class="border-0">Date</th>
                                        <th scope="col" class="border-0">Status</th>
                                        <th scope="col" class="border-0">Total</th>
                                        <th scope="col" class="border-0">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = auth()->user()->orders()->latest()->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <span class="fw-bold">#<?php echo e($order->order_number); ?></span>
                                        </td>
                                        <td><?php echo e($order->created_at->format('M d, Y')); ?></td>
                                        <td>
                                            <?php if($order->status === 'completed'): ?>
                                                <span class="badge bg-success">Completed</span>
                                            <?php elseif($order->status === 'processing'): ?>
                                                <span class="badge bg-warning text-dark">Processing</span>
                                            <?php elseif($order->status === 'shipped'): ?>
                                                <span class="badge bg-info">Shipped</span>
                                            <?php elseif($order->status === 'cancelled'): ?>
                                                <span class="badge bg-danger">Cancelled</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary"><?php echo e(ucfirst($order->status)); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="fw-bold">$<?php echo e(number_format($order->total, 2)); ?></span>
                                        </td>
                                        <td>
                                            <a href="<?php echo e(route('orders.show', $order)); ?>" class="btn btn-sm btn-dark">
                                                <i class="fas fa-eye me-1"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-shopping-bag fa-4x text-muted mb-3"></i>
                            <h4 class="fw-bold mb-2">No orders found</h4>
                            <p class="text-muted mb-4">You haven't placed any orders yet.</p>
                            <a href="<?php echo e(route('products.index')); ?>" class="btn btn-dark">
                                <i class="fas fa-shopping-bag me-2"></i> Start Shopping
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Order Summary -->
            <?php if(count(auth()->user()->orders) > 0): ?>
            <div class="row mt-4">
                <div class="col-md-4 mb-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="fw-bold mb-0">Total Orders</h5>
                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                    <i class="fas fa-shopping-bag text-dark"></i>
                                </div>
                            </div>
                            <h3 class="fw-bold"><?php echo e(count(auth()->user()->orders)); ?></h3>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="fw-bold mb-0">Total Spent</h5>
                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                    <i class="fas fa-dollar-sign text-success"></i>
                                </div>
                            </div>
                            <h3 class="fw-bold">$<?php echo e(number_format(auth()->user()->orders->sum('total_amount'), 2)); ?></h3>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="fw-bold mb-0">Completed Orders</h5>
                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                    <i class="fas fa-check-circle text-success"></i>
                                </div>
                            </div>
                            <h3 class="fw-bold"><?php echo e(auth()->user()->orders->where('status', 'completed')->count()); ?></h3>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Recently Viewed Products -->
            <div class="mt-5">
                <h3 class="fw-bold mb-4">Recently Viewed Products</h3>
                
                <div class="row">
                    <?php $__empty_1 = true; $__currentLoopData = $recentlyViewedProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 border-2 hover-border-dark">
                            <div class="position-relative">
                                <img src="<?php echo e($product->image_url ?? 'https://via.placeholder.com/300x300?text='.$product->name); ?>" alt="<?php echo e($product->name); ?>" class="card-img-top">
                                <?php if($product->discount_price && $product->discount_price < $product->price): ?>
                                <div class="position-absolute top-2 start-2 bg-danger text-white text-xs fw-bold px-2 py-1 rounded">SALE</div>
                                <?php endif; ?>
                            </div>
                            <div class="card-body p-4">
                                <p class="text-muted small mb-1"><?php echo e($product->category->name ?? 'Uncategorized'); ?></p>
                                <h5 class="fw-semibold mb-1"><?php echo e($product->name); ?></h5>
                                <?php if($product->discount_price && $product->discount_price < $product->price): ?>
                                <div class="d-flex align-items-center mb-3">
                                    <p class="fw-bold me-2 mb-0">$<?php echo e(number_format($product->discount_price, 2)); ?></p>
                                    <p class="text-muted text-decoration-line-through mb-0">$<?php echo e(number_format($product->price, 2)); ?></p>
                                </div>
                                <?php else: ?>
                                <p class="fw-bold mb-3">$<?php echo e(number_format($product->price, 2)); ?></p>
                                <?php endif; ?>
                                <div class="d-flex justify-content-between align-items-center">
                                    <form action="<?php echo e(route('cart.add', $product)); ?>" method="POST">
                                        <?php echo csrf_field(); ?>
                                        <input type="hidden" name="quantity" value="1">
                                        <button type="submit" class="btn btn-dark d-flex align-items-center justify-content-center">
                                            <i class="fa-solid fa-cart-plus me-2"></i> Add to Cart
                                        </button>
                                    </form>
                                    <?php if(auth()->guard()->check()): ?>
                                    <form action="<?php echo e(route('wishlist.add', $product)); ?>" method="POST">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="btn btn-link text-dark p-0">
                                            <i class="far fa-heart fs-5"></i>
                                        </button>
                                    </form>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="col-12">
                        <div class="alert alert-info">
                            <p class="mb-0">You haven't viewed any products yet. <a href="<?php echo e(route('products.index')); ?>" class="alert-link">Start shopping</a> to see your recently viewed products here.</p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Music\brandify\brandifyng\resources\views/customer/orders/index.blade.php ENDPATH**/ ?>